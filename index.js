const config = require("./config");
const BinanceAPI = require("./binance-api");
const DiscordBot = require("./discord-bot");
const TradingStrategy = require("./strategy");

class TradingBot {
  constructor() {
    this.config = config;
    this.binanceAPI = new BinanceAPI();
    this.discordBot = new DiscordBot();
    this.strategy = new TradingStrategy(config, this.discordBot);

    // Trading simulator
    this.simulator = {
      isHavingOrder: false,
      order: {},
      lastOrderTime: 0,
      startingBalance: config.trading.simulator.startingBalance,
      currentBalance: config.trading.simulator.startingBalance,
    };

    // Set references in Discord bot for commands
    this.discordBot.setSimulator(this.simulator);
    this.discordBot.setStrategy(this.strategy);

    // Data update intervals
    this.dataUpdateInterval = null;
    this.priceCheckInterval = null;

    this.isRunning = false;
  }

  /**
   * Initialize and start the trading bot
   */
  async start() {
    try {
      console.log("🚀 Starting Advanced Binance Trading Bot...");

      // Connect to Discord
      console.log("📡 Connecting to Discord...");
      await this.discordBot.connect();

      // Send startup notification
      await this.discordBot.sendNotification(
        "🤖 **Trading Bot Started**\n✅ Discord connected\n📊 New multi-indicator strategy active"
      );

      // Initialize market data
      console.log("📈 Initializing market data...");
      await this.initializeMarketData();

      // Start data update loop
      this.startDataUpdateLoop();

      // Start WebSocket for real-time price monitoring
      console.log("🔄 Starting real-time price monitoring...");
      this.startPriceMonitoring();

      this.isRunning = true;
      console.log("✅ Trading bot is now running!");
    } catch (error) {
      console.error("❌ Error starting trading bot:", error);
      throw error;
    }
  }

  /**
   * Initialize market data for all symbols
   */
  async initializeMarketData() {
    for (const symbol of this.config.trading.symbols) {
      try {
        console.log(`📊 Loading initial data for ${symbol}...`);

        // Get historical kline data (500 periods for sufficient indicator calculation)
        const klineData = await this.binanceAPI.getKlines(symbol, "5m", 500);
        const parsedData = this.binanceAPI.parseKlineData(klineData);

        // Update strategy with historical data
        this.strategy.updateMarketData(symbol, parsedData);

        console.log(`✅ Loaded ${parsedData.length} data points for ${symbol}`);
      } catch (error) {
        console.error(`❌ Error loading data for ${symbol}:`, error.message);
      }
    }
  }

  /**
   * Start periodic data update loop
   */
  startDataUpdateLoop() {
    const updateInterval = this.config.trading.intervals.dataUpdate * 60 * 1000; // Convert to milliseconds

    this.dataUpdateInterval = setInterval(async () => {
      await this.updateMarketData();
    }, updateInterval);

    console.log(
      `🔄 Data update loop started (every ${this.config.trading.intervals.dataUpdate} minutes)`
    );
  }

  /**
   * Update market data for all symbols
   */
  async updateMarketData() {
    for (const symbol of this.config.trading.symbols) {
      try {
        // Get latest kline data
        const klineData = await this.binanceAPI.getKlines(symbol, "5m", 100);
        const parsedData = this.binanceAPI.parseKlineData(klineData);

        // Update strategy with new data
        this.strategy.updateMarketData(symbol, parsedData);
      } catch (error) {
        console.error(`Error updating data for ${symbol}:`, error.message);
      }
    }
  }

  /**
   * Start real-time price monitoring via WebSocket
   */
  startPriceMonitoring() {
    this.binanceAPI.subscribeToTrades(
      this.config.trading.symbols,
      (tradeData) => {
        this.handleTradeEvent(tradeData);
      }
    );
  }

  /**
   * Handle incoming trade events
   * @param {Object} tradeData - Trade event data
   */
  handleTradeEvent(tradeData) {
    const { symbol, price } = tradeData;

    // Check for trading signals
    this.strategy.checkTradingSignal(symbol, price, this.simulator);

    // Check price alerts
    this.strategy.checkPriceAlerts(symbol, price);

    // Check position exit conditions
    if (this.simulator.isHavingOrder && this.simulator.order.pair === symbol) {
      this.strategy.checkPositionExit(
        this.simulator.order,
        price,
        this.simulator
      );
    }
  }

  /**
   * Stop the trading bot
   */
  async stop() {
    try {
      console.log("🛑 Stopping trading bot...");

      this.isRunning = false;

      // Clear intervals
      if (this.dataUpdateInterval) {
        clearInterval(this.dataUpdateInterval);
      }

      // Disconnect WebSocket
      this.binanceAPI.disconnect();

      // Send shutdown notification
      await this.discordBot.sendNotification(
        "🛑 **Trading Bot Stopped**\n📊 All monitoring ceased"
      );

      // Disconnect Discord
      await this.discordBot.disconnect();

      console.log("✅ Trading bot stopped successfully");
    } catch (error) {
      console.error("❌ Error stopping trading bot:", error);
    }
  }

  /**
   * Get current bot status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      simulator: this.simulator,
      symbols: this.config.trading.symbols,
      strategies: this.config.trading.symbols.map((symbol) =>
        this.strategy.getStrategyStatus(symbol)
      ),
    };
  }
}

// Create and start the bot
const bot = new TradingBot();

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("\n🛑 Received SIGINT, shutting down gracefully...");
  await bot.stop();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
  await bot.stop();
  process.exit(0);
});

// Start the bot
bot.start().catch((error) => {
  console.error("💥 Fatal error starting bot:", error);
  process.exit(1);
});
